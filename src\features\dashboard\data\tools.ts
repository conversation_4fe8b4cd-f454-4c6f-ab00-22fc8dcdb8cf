import {
  IconBrandZoom,
  IconCalendarTime,
  IconCloudUpload,
  IconDeviceLaptop,
  IconEdit,
  IconFileText,
  IconHeadset,
  IconServer,
  IconUserPlus,
  IconUsers,
  IconVideo,
} from '@tabler/icons-react'

export type ToolCategory =
  | 'video-editing'
  | 'backup'
  | 'planning'
  | 'hr'
  | 'account'
  | 'support'

export interface Tool {
  id: string
  title: string
  description: string
  href: string
  icon: typeof IconEdit
  category: ToolCategory
}

export const toolCategories = [
  {
    id: 'video-editing',
    title: 'Video editing',
    description: 'Tools for video editing and production',
  },
  {
    id: 'backup',
    title: 'Back-up',
    description: 'Back-up and storage systems',
  },
  {
    id: 'planning',
    title: 'Planning',
    description: 'Project planning and management',
  },
  {
    id: 'hr',
    title: 'HR',
    description: 'Human resource tools',
  },
  {
    id: 'account',
    title: 'Account management',
    description: 'Account and access management',
  },
  {
    id: 'support',
    title: 'Support',
    description: 'Support and helpdesk tools',
  },
]

export const tools: Tool[] = [
  {
    id: 'qscan',
    title: 'QScan',
    description: 'Video content scanning and analysis',
    href: '/qscan',
    icon: IconVideo,
    category: 'video-editing',
  },
  {
    id: 'cloud-upload',
    title: 'Cloud Upload',
    description: 'Upload en beheer bestanden',
    href: '/cloud-upload',
    icon: IconCloudUpload,
    category: 'backup',
  },
  {
    id: 'meetings',
    title: 'Meetings',
    description: 'Video conferencing',
    href: '/zoom',
    icon: IconBrandZoom,
    category: 'planning',
  },
  {
    id: 'project-planning',
    title: 'Project Planning',
    description: 'Project management and planning',
    href: '/planning',
    icon: IconCalendarTime,
    category: 'planning',
  },
  {
    id: 'server-backup',
    title: 'Server Backup',
    description: 'Server back-up systems',
    href: '/server-backup',
    icon: IconServer,
    category: 'backup',
  },
  {
    id: 'hr-portal',
    title: 'HR Portal',
    description: 'HR management system',
    href: '/hr-portal',
    icon: IconUsers,
    category: 'hr',
  },
  {
    id: 'onboarding',
    title: 'Onboarding',
    description: 'New employee onboarding',
    href: '/onboarding',
    icon: IconUserPlus,
    category: 'hr',
  },
  {
    id: 'support',
    title: 'Support',
    description: 'Support ticket systeem',
    href: '/support',
    icon: IconHeadset,
    category: 'support',
  },
  {
    id: 'password-manager',
    title: 'Password manager',
    description: 'Password management',
    href: '/passwords',
    icon: IconDeviceLaptop,
    category: 'account',
  },
  {
    id: 'documentation',
    title: 'Documentation',
    description: 'Project documentatie',
    href: '/docs',
    icon: IconFileText,
    category: 'support',
  },
]
