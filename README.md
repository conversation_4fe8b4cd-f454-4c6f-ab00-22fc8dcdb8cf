# Postlounge Intranet Dashboard

Postlounge Intranet Dashboard built with Shadcn and Vite. Built with responsiveness and accessibility in mind.

![alt text](public/assets/images/shadcn-admin.png)

## Features

- Light/dark mode
- Responsive
- Accessible
- With built-in Sidebar component
- Global Search Command
- 10+ pages
- Extra custom components

## Tech Stack

**UI:** [ShadcnUI](https://ui.shadcn.com) (TailwindCSS + RadixUI)

**Build Tool:** [Vite](https://vitejs.dev/)

**Routing:** [TanStack Router](https://tanstack.com/router/latest)

**Type Checking:** [TypeScript](https://www.typescriptlang.org/)

**Linting/Formatting:** [Eslint](https://eslint.org/) & [Prettier](https://prettier.io/)

**Icons:** [Tabler Icons](https://tabler.io/icons)

**Auth (partial):** [Clerk](https://go.clerk.com/GttUAaK)

## Run Locally

Clone the project

```bash
  git clone https://github.com/postlounge/intranet_dashboard.git
```

Go to the project directory

```bash
  cd shadcn-admin
```

Install dependencies

```bash
  pnpm install
```

Start the server

```bash
  pnpm run dev
```

## Authors

[Jesse Jägers](https://github.com/jessej-dev)
[Daniel Wichers](https://github.com/dwichers)

## License

Licensed under the [MIT License](https://choosealicense.com/licenses/mit/)
