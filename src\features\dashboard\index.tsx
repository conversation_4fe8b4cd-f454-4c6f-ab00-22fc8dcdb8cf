
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { ApplicationBox } from './components/application-box'
import { tools, toolCategories, type Tool, type ToolCategory } from './data/tools'

export default function Dashboard() {
  const toolsByCategory = Object.fromEntries(
    toolCategories.map(category => [
      category.id, 
      tools.filter(tool => tool.category === category.id)
    ])
  )

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Dashboard</h1>
        </div>
        <Tabs
          orientation='vertical'
          defaultValue='overview'
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='overview'>All tools</TabsTrigger>
              {toolCategories.map(category => (
                <TabsTrigger key={category.id} value={category.id}>
                  {category.title}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          <TabsContent value='overview' className='space-y-4'>
            <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
              {tools.map(tool => {
                const Icon = tool.icon
                return (
                  <ApplicationBox
                    key={tool.id}
                    title={tool.title}
                    href={tool.href}
                    icon={<Icon className="h-full w-full" />}
                  />
                )
              })}
            </div>
          </TabsContent>

          {toolCategories.map(category => (
            <TabsContent key={category.id} value={category.id} className='space-y-4'>
              <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
                {tools
                  .filter(tool => tool.category === category.id)
                  .map(tool => {
                    const Icon = tool.icon
                    return (
                      <ApplicationBox
                        key={tool.id}
                        title={tool.title}
                        href={tool.href}
                        icon={<Icon className="h-full w-full" />}
                      />
                    )
                  })}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </Main>
    </>
  )
}

const topNav = [
  {
    title: 'Tools',
    href: '/',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Planning',
    href: 'planning',
    isActive: false,
    disabled: false,
  },
  {
    title: 'HR',
    href: 'hr',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Support',
    href: 'support',
    isActive: false,
    disabled: false,
  }
]
