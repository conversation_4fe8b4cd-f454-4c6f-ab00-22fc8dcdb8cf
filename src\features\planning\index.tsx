import { useState } from 'react'
import { format, addDays, subDays, addWeeks, subWeeks, addMonths, subMonths } from 'date-fns'
import { nl } from 'date-fns/locale'
import { ChevronLeftIcon, ChevronRightIcon, UserIcon } from 'lucide-react'
import { editRooms } from './data/rooms'
import { cn } from '@/lib/utils'

const prevDay = (date: Date) => subDays(date, 1)
const nextDay = (date: Date) => addDays(date, 1)
const prevWeek = (date: Date) => subWeeks(date, 1)
const nextWeek = (date: Date) => addWeeks(date, 1)
const prevMonth = (date: Date) => subMonths(date, 1)
const nextMonth = (date: Date) => addMonths(date, 1)
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

const topNav = [
  {
    title: 'Dag overzicht',
    href: '/planning/day',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Week overzicht',
    href: '/planning/week',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Maand overzicht',
    href: '/planning/month',
    isActive: false,
    disabled: false,
  },
]

export default function Planning() {
  const [activeView, setActiveView] = useState('day')
  const [planningDate, setPlanningDate] = useState(new Date())
  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Planning</h1>
        </div>
        <Tabs
          orientation='vertical'
          value={activeView}
          onValueChange={setActiveView}
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='day'>Dag overzicht</TabsTrigger>
              <TabsTrigger value='week'>Week overzicht</TabsTrigger>
              <TabsTrigger value='month'>Maand overzicht</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='day' className='space-y-4'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex gap-2'>
                <Button variant='outline' onClick={() => setPlanningDate(prevDay(planningDate))}>
                  <ChevronLeftIcon className="h-4 w-4" />
                  Vorige dag
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(new Date())}>
                  Vandaag
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(nextDay(planningDate))}>
                  Volgende dag
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
              <div className='flex gap-2'>
                <Button>Reservering toevoegen</Button>
              </div>
            </div>
            <div className='grid gap-4'>
              <Card>
                <CardHeader>
                  <CardTitle>{format(planningDate, 'EEEE d MMMM yyyy', { locale: nl })}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {editRooms.map(room => (
                      <Card key={room.id}>
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            <span>{room.name}</span>
                            <span 
                              className={cn(
                                "h-3 w-3 rounded-full",
                                room.status === 'available' ? "bg-green-500" : "bg-red-500"
                              )} 
                            />
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          {room.status === 'occupied' && room.editor && (
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <UserIcon className="h-4 w-4" />
                                <span className="text-sm font-medium">{room.editor.name}</span>
                              </div>
                              <p className="text-sm text-muted-foreground">{room.editor.project}</p>
                            </div>
                          )}
                          {room.status === 'available' && (
                            <p className="text-sm text-muted-foreground">Beschikbaar</p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value='week' className='space-y-4'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex gap-2'>
                <Button variant='outline' onClick={() => setPlanningDate(prevWeek(planningDate))}>
                  <ChevronLeftIcon className="h-4 w-4" />
                  Vorige week
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(new Date())}>
                  Deze week
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(nextWeek(planningDate))}>
                  Volgende week
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className='grid gap-4'>
              <Card>
                <CardHeader>
                  <CardTitle>Week {format(planningDate, 'w', { locale: nl })}</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Week view content here */}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value='month' className='space-y-4'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex gap-2'>
                <Button variant='outline' onClick={() => setPlanningDate(prevMonth(planningDate))}>
                  <ChevronLeftIcon className="h-4 w-4" />
                  Vorige maand
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(new Date())}>
                  Deze maand
                </Button>
                <Button variant='outline' onClick={() => setPlanningDate(nextMonth(planningDate))}>
                  Volgende maand
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className='grid gap-4'>
              <Card>
                <CardHeader>
                  <CardTitle>{format(planningDate, 'MMMM yyyy', { locale: nl })}</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Month calendar here */}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}
