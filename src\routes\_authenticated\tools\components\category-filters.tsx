import { Button } from '@/components/ui/button'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { toolCategories } from '../data/tools'

interface CategoryFiltersProps {
  activeCategory: string | null
  onCategoryChange: (category: string | null) => void
}

export function CategoryFilters({
  activeCategory,
  onCategoryChange,
}: CategoryFiltersProps) {
  return (
    <ScrollArea className="w-full whitespace-nowrap rounded-md border">
      <div className="flex w-max space-x-4 p-4">
        <Button
          variant={activeCategory === null ? "default" : "outline"}
          onClick={() => onCategoryChange(null)}
        >
          All tools
        </Button>
        {toolCategories.map((category) => (
          <Button
            key={category.id}
            variant={activeCategory === category.id ? "default" : "outline"}
            onClick={() => onCategoryChange(category.id)}
          >
            {category.title}
          </Button>
        ))}
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  )
}
