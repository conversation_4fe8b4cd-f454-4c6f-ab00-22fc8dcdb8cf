export type EditRoom = {
  name: string
  id: string
  status: 'available' | 'occupied'
  editor?: {
    name: string
    project: string
  }
}

export const editRooms: EditRoom[] = [
  {
    id: 'biologie',
    name: 'Biologie',
    status: 'occupied',
    editor: {
      name: '<PERSON>',
      project: 'Natuurdocumentaire - De Veluwe'
    }
  },
  {
    id: 'natuurkunde',
    name: 'Natuurku<PERSON>',
    status: 'available'
  },
  {
    id: 'tekenen',
    name: 'Te<PERSON><PERSON>',
    status: 'occupied',
    editor: {
      name: '<PERSON>',
      project: 'Kunstacademie Promo'
    }
  },
  {
    id: 'gymzaal',
    name: 'Gymzaal',
    status: 'occupied',
    editor: {
      name: '<PERSON>',
      project: 'Sportcentrum Opening'
    }
  },
  {
    id: 'wiskunde',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    status: 'available'
  },
  {
    id: 'scheikunde',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    status: 'occupied',
    editor: {
      name: '<PERSON>',
      project: 'Lab Safety Training'
    }
  },
  {
    id: 'nederlands',
    name: 'Nederland<PERSON>',
    status: 'occupied',
    editor: {
      name: '<PERSON>',
      project: 'Literatuurfestival 2025'
    }
  },
  {
    id: 'engel<PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    status: 'available'
  }
]
