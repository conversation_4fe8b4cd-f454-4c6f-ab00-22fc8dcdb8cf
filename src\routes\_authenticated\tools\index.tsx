import { createFileRoute } from '@tanstack/react-router'
import { Main } from '@/components/layout/main'
import { Header } from '@/components/layout/header'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { TopNav } from '@/components/layout/top-nav'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ToolCard } from './components/tool-card'
import { tools, toolCategories } from './data/tools'

const topNav = [
  {
    title: 'All tools',
    href: '/tools',
    isActive: true,
    disabled: false,
  },
  {
    title: 'Video editing',
    href: '/tools?category=video-editing',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Back-up',
    href: '/tools?category=backup',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Planning',
    href: '/tools?category=planning',
    isActive: false,
    disabled: false,
  },
  {
    title: 'HR',
    href: '/tools?category=hr',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Account',
    href: '/tools?category=account',
    isActive: false,
    disabled: false,
  },
  {
    title: 'Support',
    href: '/tools?category=support',
    isActive: false,
    disabled: false,
  },
]

export const Route = createFileRoute('/_authenticated/tools/')({
  component: ToolsIndex
})

function ToolsIndex() {
  return (
    <>
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>Tools & Resources</h1>
        </div>
        <Tabs
          orientation='vertical'
          defaultValue='all'
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='all'>All tools</TabsTrigger>
              <TabsTrigger value='video-editing'>Video editing</TabsTrigger>
              <TabsTrigger value='backup'>Back-up</TabsTrigger>
              <TabsTrigger value='planning'>Planning</TabsTrigger>
              <TabsTrigger value='hr'>HR</TabsTrigger>
              <TabsTrigger value='account'>Account</TabsTrigger>
              <TabsTrigger value='support'>Support</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='all' className='space-y-4'>
            <div className='grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {tools.map(tool => {
                const Icon = tool.icon
                return (
                  <ToolCard
                    key={tool.id}
                    title={tool.title}
                    description={tool.description}
                    href={tool.href}
                    icon={<Icon className="h-6 w-6" />}
                  />
                )
              })}
            </div>
          </TabsContent>

          {toolCategories.map(category => (
            <TabsContent key={category.id} value={category.id} className='space-y-4'>
              <div className='grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
                {tools
                  .filter(tool => tool.category === category.id)
                  .map(tool => {
                    const Icon = tool.icon
                    return (
                      <ToolCard
                        key={tool.id}
                        title={tool.title}
                        description={tool.description}
                        href={tool.href}
                        icon={<Icon className="h-6 w-6" />}
                      />
                    )
                  })}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </Main>
    </>
  )
}
