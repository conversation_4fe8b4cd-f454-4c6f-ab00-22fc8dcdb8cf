import { Card, CardHeader } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface ApplicationBoxProps {
  title: string
  icon: React.ReactNode
  href: string
  className?: string
}

export function ApplicationBox({ title, icon, href, className }: ApplicationBoxProps) {
  return (
    <a
      href={href}
      className={cn(
        'block transition-colors hover:bg-accent',
        className
      )}
    >
      <Card className="h-full">
        <CardHeader className="flex flex-col items-center justify-center space-y-2 text-center">
          <div className="h-12 w-12 text-primary">
            {icon}
          </div>
          <h3 className="font-medium">{title}</h3>
        </CardHeader>
      </Card>
    </a>
  )
}
